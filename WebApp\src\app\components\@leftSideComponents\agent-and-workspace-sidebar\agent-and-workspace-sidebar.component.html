<!-- Agent and Workspace Container -->
<div class="agent-workspace-container h-full w-full p-4" [ngClass]="{
    'bg-[#2b2b33] text-white': themeService.isDarkMode(),
    'bg-[var(--background-white)] text-[var(--text-dark)]':
      !themeService.isDarkMode()
  }">
  <!-- Header -->
  <div class="mb-6">
    <h2 class="text-xl font-bold mb-2" [ngClass]="{
        'text-white': themeService.isDarkMode(),
        'text-[var(--text-dark)]': !themeService.isDarkMode()
      }">
      AI Hub
    </h2>
    <p class="text-sm mb-4" [ngClass]="{
        'text-gray-300': themeService.isDarkMode(),
        'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
      }">
      Select an agent or choose a workspace
    </p>
  </div>

  <!-- ACCORDION SECTIONS -->
  <div class="accordion-container space-y-4">

    <!-- AGENTS ACCORDION SECTION -->
    <div class="accordion-section">
      <!-- Agents Header -->
      <div (click)="toggleAccordionSection('agents')"
        class="accordion-header flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200"
        [ngClass]="{
          'bg-[#3a3a45] hover:bg-[#404050] border border-[#4a4a55]': themeService.isDarkMode(),
          'bg-[var(--background-white)] hover:bg-[var(--hover-blue-gray)] border border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center gap-3">
          <h3 class="font-semibold text-base" [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }">
            {{ accordionSections.agents.title }}
          </h3>
        </div>

        <!-- Chevron Icon -->
        <div class="transition-transform duration-200" [ngClass]="{
            'rotate-180': accordionSections.agents.isExpanded,
            'rotate-0': !accordionSections.agents.isExpanded
          }">
          <i class="ri-arrow-down-s-line text-lg" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
        </div>
      </div>

      <!-- Agents Content -->
      <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" [ngClass]="{
          'max-h-96 opacity-100 mt-3': accordionSections.agents.isExpanded,
          'max-h-0 opacity-0 mt-0': !accordionSections.agents.isExpanded
        }">
        <!-- Loading State for Agents -->
        <div *ngIf="isLoadingAgents" class="flex items-center justify-center py-8">
          <div class="flex items-center gap-2">
            <div class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin">
            </div>
            <span class="text-sm" [ngClass]="{
                'text-gray-300': themeService.isDarkMode(),
                'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }">
              Loading agents...
            </span>
          </div>
        </div>

        <!-- Agent List -->
        <div *ngIf="!isLoadingAgents && !agentsError && agents.length > 0" class="space-y-3">
          @for (agentName of agents; track $index) {
          <div (click)="navigateToAgentChat(agentName)"
            class="agent-card py-1 px-2  rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md"
            [ngClass]="{
              'bg-[#3a3a45] border-[#4a4a55] hover:border-[#00c39a] hover:bg-[#404050]':
                themeService.isDarkMode(),
              'bg-[var(--background-white)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:bg-[var(--background-light-gray)]':
                !themeService.isDarkMode()
            }">
            <!-- Agent Icon and Name -->
            <div class="flex items-center gap-3">
              <div class="w-5 h-5 rounded-full flex items-center justify-center" [ngClass]="{
                  'bg-[#00c39a]': themeService.isDarkMode(),
                  'bg-[var(--primary-purple)]': !themeService.isDarkMode()
                }">
                <i class="ri-robot-line text-white"></i>
              </div>

              <div class="flex-1">
                <h3 class="font-normal text-base" [ngClass]="{
                    'text-white': themeService.isDarkMode(),
                    'text-[var(--text-dark)]': !themeService.isDarkMode()
                  }">
                  {{ formatAgentName(agentName) }}
                </h3>
              </div>

              <!-- Arrow Icon -->
              <div class="text-lg" [ngClass]="{
                  'text-gray-400': themeService.isDarkMode(),
                  'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
                }">
                <i class="ri-arrow-right-s-line"></i>
              </div>
            </div>
          </div>
          }
        </div>


      </div>
    </div>

    <!-- WORKSPACES ACCORDION SECTION -->
    <div class="accordion-section">
      <!-- Workspaces Header -->
      <div (click)="toggleAccordionSection('workspaces')"
        class="accordion-header flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200"
        [ngClass]="{
          'bg-[#3a3a45] hover:bg-[#404050] border border-[#4a4a55]': themeService.isDarkMode(),
          'bg-[var(--background-white)] hover:bg-[var(--hover-blue-gray)] border border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center gap-3">
          <h3 class="font-semibold text-base" [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }">
            {{ accordionSections.workspaces.title }}
          </h3>
        </div>

        <!-- Chevron Icon -->
        <div class="transition-transform duration-200" [ngClass]="{
            'rotate-180': accordionSections.workspaces.isExpanded,
            'rotate-0': !accordionSections.workspaces.isExpanded
          }">
          <i class="ri-arrow-down-s-line text-lg" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
        </div>
      </div>

      <!-- Workspaces Content -->
      <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" [ngClass]="{
          'max-h-96 opacity-100 mt-3': accordionSections.workspaces.isExpanded,
          'max-h-0 opacity-0 mt-0': !accordionSections.workspaces.isExpanded
        }">

        <!-- Workspace List -->
        <div *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length > 0" class="space-y-3">
          @for (workspace of workspaces; track workspace.id) {
          <div (click)="navigateToWorkspace(workspace)"
            class="workspace-card py-1 px-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md"
            [ngClass]="{
              'bg-[#3a3a45] border-[#4a4a55] hover:border-[#00c39a] hover:bg-[#404050]':
                themeService.isDarkMode(),
              'bg-[var(--background-white)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:bg-[var(--background-light-gray)]':
                !themeService.isDarkMode()
            }">
            <!-- Workspace Icon and Name -->
            <div class="flex items-center gap-3">
              <div class="w-5 h-5 rounded-full flex items-center justify-center" [ngClass]="{
                  'bg-[#00c39a]': themeService.isDarkMode(),
                  'bg-[var(--primary-purple)]': !themeService.isDarkMode()
                }">
                <i class="ri-building-line text-white"></i>
              </div>
              <div class="flex-1">
                <h3 class="font-normal text-base" [ngClass]="{
                    'text-white': themeService.isDarkMode(),
                    'text-[var(--text-dark)]': !themeService.isDarkMode()
                  }">
                  {{ workspace.title }}
                </h3>
              </div>

              <!-- Arrow Icon -->
              <div class="text-lg" [ngClass]="{
                  'text-gray-400': themeService.isDarkMode(),
                  'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
                }">
                <i class="ri-arrow-right-s-line"></i>
              </div>
            </div>
          </div>
          }
        </div>

        <!-- Empty State for Workspaces -->
        <div *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length === 0" class="text-center py-8">
          <div class="">
            <i class="ri-building-line text-4xl" [ngClass]="{
                'text-gray-500': themeService.isDarkMode(),
                'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }"></i>
          </div>
          <p class="text-sm" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }">
            No workspaces available
          </p>
          <button (click)="retryLoadWorkspaces()"
            class="mt-4 px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-[var(--secondary-purple)] transition-colors">
            Refresh
          </button>
        </div>

        <!-- Error State for Workspaces -->
        <div *ngIf="workspacesError" class="text-center py-8">
          <div class="">
            <i class="ri-error-warning-line text-4xl" [ngClass]="{
                'text-red-400': themeService.isDarkMode(),
                'text-red-500': !themeService.isDarkMode()
              }"></i>
          </div>
          <p class="text-sm mb-4" [ngClass]="{
              'text-red-400': themeService.isDarkMode(),
              'text-red-500': !themeService.isDarkMode()
            }">
            {{ workspacesError }}
          </p>
          <button (click)="retryLoadWorkspaces()"
            class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-[var(--secondary-purple)] transition-colors">
            Retry
          </button>
        </div>
      </div>
    </div>

  </div>
</div>
